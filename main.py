import os
import sys
from datetime import datetime
from src.data.loader import DataLoader
import numpy as np
import pandas as pd
import cProfile  # 导入 cProfile
import pstats    # 导入 pstats
import io        # 导入 io

# 回测参数配置
BACKTEST_PARAMS = {
    'start_date': '2022-11-27',
    'end_date': '2024-11-25',
    'initial_capital': 100000,
    'data_frequency': 'minute',  # 'day', 'minute', 'tick'
    'commission_rate': 0.0001,
    'slippage_rate': 0.001
}

# 固定参数（当不进行优化时使用）
FIXED_PARAMS = {
    'window': 120,        # 使用前5个交易日的数据
    'std_dev_mult': 10,  # 标准差倍数
    'max_pos_size': 1.0    # 最大持仓比例
}

# 参数优化范围
OPTIMIZATION_PARAMS = {
    'window': np.arange(30, 170, 20).tolist(),        # 窗口大小范围（1-10个交易日）
    'std_dev_mult': np.arange(0, 10.1, 1).tolist(),  # 标准差倍数范围
    'max_pos_size': [1.0]    # 最大持仓比例范围
}

def run_optimization(data):
    """运行参数优化
    
    Args:
        data: 已加载的回测数据
        
    Returns:
        tuple: (最优参数, 最优参数的回测结果)
    """
    try:
        # 导入优化器
        from src.optimization.optimizer import StrategyOptimizer
        
        # 初始化优化器
        optimizer = StrategyOptimizer(data=data, backtest_params=BACKTEST_PARAMS)
        
        # 运行优化
        print("开始参数优化...")
        best_params, best_backtest_results, all_results = optimizer.optimize(
            param_ranges=OPTIMIZATION_PARAMS,
            n_jobs=8,  
            metric='total_returns'  # 只使用总收益率作为优化指标
        )
        
        # 打印最优参数
        print("\n最优参数组合:")
        for param, value in best_params.items():
            print(f"{param}: {value}")
            
        # 可视化优化结果
        optimizer.plot_optimization_results(all_results)
            
        return best_params, best_backtest_results
        
    except Exception as e:
        print(f"优化过程出错: {str(e)}")
        raise

def run_fixed_params(data):
    """使用固定参数运行回测
    
    Args:
        data: 已加载的回测数据
        
    Returns:
        tuple: (固定参数, 回测结果)
    """
    try:
        # 导入策略和引擎
        from src.strategy.pairs_strategy import PairsStrategy
        from src.engine.engine import BacktestEngine
        
        # 初始化策略
        strategy = PairsStrategy(**FIXED_PARAMS)
        
        # 初始化引擎
        engine = BacktestEngine(
            strategy=strategy,
            data=data,
            initial_capital=BACKTEST_PARAMS['initial_capital'],
            commission_rate=BACKTEST_PARAMS['commission_rate'],
            slippage_rate=BACKTEST_PARAMS['slippage_rate']
        )
        
        # 运行回测
        print(f"\n使用固定参数运行回测:")
        for param, value in FIXED_PARAMS.items():
            print(f"{param}: {value}")
            
        results = engine.run()
        
        return FIXED_PARAMS, results
        
    except Exception as e:
        print(f"回测过程出错: {str(e)}")
        raise

def analyze_and_visualize_results(results, backtest_params):
    """分析和可视化回测结果"""
    try:
        from src.analysis.performance import PerformanceAnalyzer
        from src.analysis.visualizer import Visualizer
        
        # 分析结果
        analyzer = PerformanceAnalyzer(results, backtest_params=backtest_params)
        performance_metrics = analyzer.analyze()
        
        # 可视化结果
        visualizer = Visualizer(results)
        current_date = datetime.now().strftime('%Y%m%d')
        
        # 保存结果
        os.makedirs('results', exist_ok=True)
        visualizer.plot_results(save_dir='results')
        performance_metrics.to_csv(os.path.join('results', f'backtest_report_{current_date}.csv'))
        
        print("\n性能指标:")
        print(performance_metrics)
        
        return performance_metrics
        
    except Exception as e:
        print(f"结果分析过程出错: {str(e)}")
        raise

def main():
    """主函数"""
    try:
        # 初始化数据加载器
        data_loader = DataLoader()
        
        # 加载回测数据（只加载一次）
        print("正在加载数据...")
        data = data_loader.load_data(
            start_date=BACKTEST_PARAMS['start_date'],
            end_date=BACKTEST_PARAMS['end_date'],
            frequency=BACKTEST_PARAMS['data_frequency']
        )
        print(f"数据加载完成，范围: {data.index[0]} 到 {data.index[-1]}")
        
        # 询问用户是否进行参数优化
        while True:
            optimize = input("\n是否进行参数优化? (y/n): ").strip().lower()
            if optimize in ['y', 'n']:
                break
            print("请输入 'y' 或 'n'")
        
        # 根据用户选择运行不同的流程
        if optimize == 'y':
            # 运行参数优化并获取最优回测结果
            print("\n=== 第一阶段：参数优化 ===")
            best_params, best_backtest_results = run_optimization(data)
            
            # 分析和可视化最优参数的回测结果
            print("\n=== 第二阶段：分析最优参数回测结果 ===")
            performance_metrics = analyze_and_visualize_results(best_backtest_results, BACKTEST_PARAMS)
            
            return best_params, performance_metrics
        else:
            # 使用固定参数运行回测
            print("\n=== 使用固定参数运行回测 ===")
            fixed_params, backtest_results = run_fixed_params(data)
            
            # 分析和可视化回测结果
            print("\n=== 分析回测结果 ===")
            performance_metrics = analyze_and_visualize_results(backtest_results, BACKTEST_PARAMS)
            
            return fixed_params, performance_metrics
        
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        raise

if __name__ == "__main__":
    main() # 执行主函数
